import prisma from '../config/database';
import redisClient from '../config/redis';
import { AppError } from '../utils/errorHandler';
import { Stock } from '@prisma/client';

// Cache TTL in seconds (1 hour)
const CACHE_TTL = 3600;

/**
 * Get all stock items with filtering and pagination
 * @param options - Query options for filtering and pagination
 * @returns Paginated stock items
 */
export const getStocks = async (options: {
  page?: number;
  limit?: number;
  search?: string;
  type?: string;
  gsm?: number;
  bf?: number;
  minPrice?: number;
  maxPrice?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}) => {
  const {
    page = 1,
    limit = 10,
    search,
    type,
    gsm,
    bf,
    minPrice,
    maxPrice,
    sortBy = 'createdAt',
    sortOrder = 'desc',
  } = options;

  const skip = (page - 1) * limit;

  // Build where clause
  const where: any = {};

  // Add search functionality
  if (search && search.trim()) {
    where.OR = [
      { type: { contains: search, mode: 'insensitive' } },
      { gsm: { equals: isNaN(parseInt(search)) ? undefined : parseInt(search) } },
      { bf: { equals: isNaN(parseInt(search)) ? undefined : parseInt(search) } },
    ].filter(condition => Object.values(condition)[0] !== undefined);
  }

  if (type) where.type = type;
  if (gsm) where.gsm = gsm;
  if (bf) where.bf = bf;
  if (minPrice) where.pricePerRoll = { gte: minPrice };
  if (maxPrice) {
    where.pricePerRoll = {
      ...where.pricePerRoll,
      lte: maxPrice,
    };
  }

  // Build order by
  const orderBy: any = {};
  orderBy[sortBy] = sortOrder;

  // // Generate cache key based on query parameters
  // const cacheKey = `stocks:${JSON.stringify({ page, limit, where, orderBy })}`;

  // // Try to get from cache first
  // const cachedData = await redisClient.get(cacheKey);
  // if (cachedData) {
  //   return JSON.parse(cachedData.toString());
  // }

  // If not in cache, fetch from database
  const [stocks, total] = await Promise.all([
    prisma.stock.findMany({
      where,
      orderBy,
      skip,
      take: limit,
    }),
    prisma.stock.count({ where }),
  ]);

  // Calculate pagination info
  const totalPages = Math.ceil(total / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  const result = {
    stocks,
    pagination: {
      total,
      page,
      limit,
      totalPages,
      hasNextPage,
      hasPrevPage,
    },
  };

  return result;
};

/**
 * Get stock by ID
 * @param id - Stock ID
 * @returns Stock item
 */
export const getStockById = async (id: string): Promise<Stock> => {
  // Try to get from cache first
  const cacheKey = `stock:${id}`;
  const cachedStock = await redisClient.get(cacheKey);

  if (cachedStock) {
    return JSON.parse(cachedStock.toString());
  }

  // If not in cache, fetch from database
  const stock = await prisma.stock.findUnique({
    where: { id },
  });

  if (!stock) {
    throw new AppError('Stock not found', 404);
  }

  // Cache the result
  await redisClient.set(cacheKey, JSON.stringify(stock), {
    EX: CACHE_TTL,
  });

  return stock;
};

/**
 * Create new stock item
 * @param data - Stock data
 * @returns Created stock item
 */
export const createStock = async (data: {
  type: string;
  gsm: number;
  bf: number;
  rollsAvailable: number;
  immediatePrice: number;
  thirtyDayPrice: number;
  sixtyDayPrice: number;
}): Promise<Stock> => {
  try {
    const stock = await prisma.stock.create({
      data,
    });

    // Invalidate relevant cache keys
    await invalidateStockCache();

    return stock;
  } catch (error: any) {
    // Handle unique constraint violation
    if (error.code === 'P2002') {
      throw new AppError('Stock with this type, GSM, and BF already exists', 409);
    }
    throw error;
  }
};

/**
 * Update stock item
 * @param id - Stock ID
 * @param data - Stock data to update
 * @returns Updated stock item
 */
export const updateStock = async (
  id: string,
  data: {
    rollsAvailable?: number;
    immediatePrice?: number;
    thirtyDayPrice?: number;
    sixtyDayPrice?: number;
  }
): Promise<Stock> => {
  // Check if stock exists
  const existingStock = await prisma.stock.findUnique({
    where: { id },
  });

  if (!existingStock) {
    throw new AppError('Stock not found', 404);
  }

  // Update stock
  const updatedStock = await prisma.stock.update({
    where: { id },
    data,
  });

  // Invalidate cache
  await invalidateStockCache(id);

  return updatedStock;
};

/**
 * Delete stock item
 * @param id - Stock ID
 * @returns Deleted stock item
 */
export const deleteStock = async (id: string): Promise<Stock> => {
  try {
    // Check if stock exists
    const existingStock = await prisma.stock.findUnique({
      where: { id },
      include: {
        cartItems: { select: { id: true } },
        orderItems: { select: { id: true } },
      },
    });

    if (!existingStock) {
      throw new AppError('Stock not found', 404);
    }

    // Check if stock is referenced in cart or orders
    if (existingStock.cartItems.length > 0) {
      throw new AppError('Cannot delete stock that is in users\' carts', 400);
    }

    if (existingStock.orderItems.length > 0) {
      throw new AppError('Cannot delete stock that is referenced in orders', 400);
    }

    // Delete stock
    const deletedStock = await prisma.stock.delete({
      where: { id },
    });

    // Invalidate cache
    await invalidateStockCache(id);

    return deletedStock;
  } catch (error: any) {
    // If error is not an AppError, it's likely a database constraint error
    if (!(error instanceof AppError)) {
      throw new AppError('Cannot delete stock that is referenced in carts or orders', 400);
    }
    throw error;
  }
};

/**
 * Check if stock is available in sufficient quantity
 * @param stockId - Stock ID
 * @param quantity - Quantity to check
 * @returns Boolean indicating if stock is available
 */
export const checkStockAvailability = async (
  stockId: string,
  quantity: number
): Promise<boolean> => {
  const stock = await getStockById(stockId);
  return stock.rollsAvailable >= quantity;
};

/**
 * Invalidate stock cache
 * @param id - Optional stock ID to invalidate specific stock
 */
export const invalidateStockCache = async (id?: string) => {
  if (id) {
    // Invalidate specific stock
    await redisClient.del(`stock:${id}`);
  }

  // Invalidate all stock lists (using pattern matching)
  const keys = await redisClient.keys('stocks:*');
  if (keys.length > 0) {
    await redisClient.del(keys);
  }
};

/**
 * Validate stock data
 * @param data - Stock data to validate
 * @returns Validation result with errors array
 */
const validateStockData = (data: any): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  // Check required fields
  if (!data.type) {
    errors.push('Type is required');
  } else if (!['NS', 'GY'].includes(data.type)) {
    errors.push('Type must be either NS or GY');
  }

  // Validate GSM
  if (isNaN(data.gsm) || data.gsm <= 0) {
    errors.push('GSM must be a positive number');
  } else {
    const validGSM = [80, 100, 120, 140, 180, 200, 230, 250];
    if (!validGSM.includes(data.gsm)) {
      errors.push(`GSM must be one of the following values: ${validGSM.join(', ')}`);
    }
  }

  // Validate BF (Bursting Factor)
  if (isNaN(data.bf) || data.bf <= 0) {
    errors.push('BF must be a positive number');
  } else {
    const validBF = [16, 18, 20, 22, 25, 28];
    if (!validBF.includes(data.bf)) {
      errors.push(`BF must be one of the following values: ${validBF.join(', ')}`);
    }
  }

  // Validate rollsAvailable
  if (isNaN(data.rollsAvailable) || data.rollsAvailable < 0) {
    errors.push('Rolls available must be a non-negative number');
  }

  // Validate immediatePrice
  if (isNaN(data.immediatePrice) || data.immediatePrice <= 0) {
    errors.push('Immediate price must be a positive number');
  }

  // Validate thirtyDayPrice
  if (isNaN(data.thirtyDayPrice) || data.thirtyDayPrice <= 0) {
    errors.push('30-day price must be a positive number');
  }

  // Validate sixtyDayPrice
  if (isNaN(data.sixtyDayPrice) || data.sixtyDayPrice <= 0) {
    errors.push('60-day price must be a positive number');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Import stock items from CSV
 * @param data - Array of stock data from CSV
 * @param errors - Array to store validation errors
 * @param userId - ID of the user performing the import
 * @returns Object with counts of imported and failed items
 */
export const importStockFromCSV = async (
  data: any[],
  errors: { row: number; message: string }[],
  userId: string
): Promise<{ importedCount: number; failedCount: number }> => {
  let importedCount = 0;
  let failedCount = 0;

  for (const row of data) {
    try {
      // Convert string values to appropriate types
      const stockData = {
        type: row.type?.trim(),
        gsm: parseInt(row.gsm, 10),
        bf: parseInt(row.bf, 10),
        rollsAvailable: parseInt(row.rollsAvailable, 10),
        immediatePrice: parseFloat(row.immediatePrice),
        thirtyDayPrice: parseFloat(row.thirtyDayPrice),
        sixtyDayPrice: parseFloat(row.sixtyDayPrice)
      };

      // Validate the data
      const validationResult = validateStockData(stockData);
      if (!validationResult.isValid) {
        errors.push({
          row: row.rowIndex,
          message: validationResult.errors.join(', ')
        });
        failedCount++;
        continue;
      }

      // Check if stock with same type, gsm, and bf already exists
      const existingStock = await prisma.stock.findFirst({
        where: {
          type: stockData.type,
          gsm: stockData.gsm,
          bf: stockData.bf
        }
      });

      if (existingStock) {
        // Update existing stock
        await prisma.stock.update({
          where: { id: existingStock.id },
          data: {
            rollsAvailable: stockData.rollsAvailable,
            immediatePrice: stockData.immediatePrice,
            thirtyDayPrice: stockData.thirtyDayPrice,
            sixtyDayPrice: stockData.sixtyDayPrice
          }
        });
      } else {
        // Create new stock
        await prisma.stock.create({
          data: stockData
        });
      }

      importedCount++;
    } catch (error: any) {
      console.error(`Error processing row ${row.rowIndex}:`, error);
      errors.push({
        row: row.rowIndex,
        message: `Error processing row: ${error.message}`
      });
      failedCount++;
    }
  }

  // Invalidate cache after import
  await invalidateStockCache();

  return { importedCount, failedCount };
};
